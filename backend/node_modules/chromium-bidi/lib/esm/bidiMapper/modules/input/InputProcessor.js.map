{"version": 3, "file": "InputProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/input/InputProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AACH,OAAO,EAEL,wBAAwB,EACxB,sBAAsB,EAEtB,6BAA6B,EAE7B,mBAAmB,GACpB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAC,MAAM,EAAC,MAAM,0BAA0B,CAAC;AAEhD,OAAO,EAAC,gBAAgB,EAAC,MAAM,8BAA8B,CAAC;AAI9D,OAAO,EAAC,iBAAiB,EAAC,MAAM,+BAA+B,CAAC;AAEhE,MAAM,OAAO,cAAc;IAChB,uBAAuB,CAAyB;IAEhD,kBAAkB,GAAG,IAAI,iBAAiB,EAAE,CAAC;IAEtD,YAAY,sBAA8C;QACxD,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAAsC;QAEtC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,IAAI,gBAAgB,CACrC,UAAU,EACV,IAAI,CAAC,uBAAuB,EAC5B,MAAM,CAAC,OAAO,EACd,MAAM,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAC3D,CAAC;QACF,MAAM,UAAU,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAAsC;QAEtC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3D,MAAM,UAAU,GAAG,IAAI,gBAAgB,CACrC,UAAU,EACV,IAAI,CAAC,uBAAuB,EAC5B,MAAM,CAAC,OAAO,EACd,MAAM,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAC3D,CAAC;QACF,MAAM,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QACtE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC3C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAgC;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAU1D,IAAI,MAAM,CAAC;QACX,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,KAAK,CAAC,YAAY,CAC/B,MAAM,CAAC,SAAS,QAAQ,CAAgB,cAAsB;gBAC5D,IAAI,CAAC,CAAC,IAAI,YAAY,gBAAgB,CAAC,EAAE,CAAC;oBACxC,IAAI,IAAI,YAAY,OAAO,EAAE,CAAC;wBAC5B,iCAAyB;oBAC3B,CAAC;oBACD,8BAAsB;gBACxB,CAAC;gBACD,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBACzB,8BAAsB;gBACxB,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,kCAA0B;gBAC5B,CAAC;gBACD,IAAI,cAAc,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACzC,kCAA0B;gBAC5B,CAAC;gBACD,OAAO;YACT,CAAC,CAAC,EACF,KAAK,EACL,MAAM,CAAC,OAAO,EACd,CAAC,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAC,CAAC,CAC/C,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,mBAAmB,CAC3B,0BAA0B,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CACpD,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QAClC,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACpC,QAAQ,MAAM,CAAC,MAAM,CAAC,KAAkB,EAAE,CAAC;gBACzC,2BAAmB,CAAC,CAAC,CAAC;oBACpB,MAAM,IAAI,sBAAsB,CAC9B,0BAA0B,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CACpD,CAAC;gBACJ,CAAC;gBACD,8BAAsB,CAAC,CAAC,CAAC;oBACvB,MAAM,IAAI,6BAA6B,CACrC,WAAW,MAAM,CAAC,OAAO,CAAC,QAAQ,iBAAiB,CACpD,CAAC;gBACJ,CAAC;gBACD,2BAAmB,CAAC,CAAC,CAAC;oBACpB,MAAM,IAAI,6BAA6B,CACrC,iBAAiB,MAAM,CAAC,OAAO,CAAC,QAAQ,qBAAqB,CAC9D,CAAC;gBACJ,CAAC;gBACD,+BAAuB,CAAC,CAAC,CAAC;oBACxB,MAAM,IAAI,6BAA6B,CACrC,iBAAiB,MAAM,CAAC,OAAO,CAAC,QAAQ,cAAc,CACvD,CAAC;gBACJ,CAAC;gBACD,+BAAuB,CAAC,CAAC,CAAC;oBACxB,MAAM,IAAI,6BAA6B,CACrC,2DAA2D,CAC5D,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED;;;;WAIG;QACH,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,wEAAwE;YACxE,8BAA8B;YAC9B,MAAM,KAAK,CAAC,YAAY,CACtB,MAAM,CAAC,SAAS,aAAa;gBAC3B,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC7B,IAAI,CAAC,aAAa,CAChB,IAAI,KAAK,CAAC,QAAQ,EAAE;wBAClB,OAAO,EAAE,IAAI;qBACd,CAAC,CACH,CAAC;oBACF,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC,KAAK,CAAC;gBAEtC,gFAAgF;gBAChF,IAAI,CAAC,aAAa,CAChB,IAAI,KAAK,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CACpD,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC,EACF,KAAK,EACL,MAAM,CAAC,OAAO,CACf,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,yEAAyE;QACzE,cAAc;QACd,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAA0B,MAAM,KAAK,CAAC,YAAY,CAC5D,MAAM,CAAC,SAAS,QAAQ,CAAyB,KAAa;gBAC5D,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC,CAAC,EACF,KAAK,EACL,MAAM,CAAC,OAAO,EACd,CAAC,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAC,CAAC,2CAE7B,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;YAClC,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACpC,MAAM;YACR,CAAC;YAED,MAAM,EAAC,MAAM,EAAC,GAAsB,MAAM,CAAC,MAAM,CAAC;YAClD,MAAM,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;YAC7B,MAAM,EAAC,IAAI,EAAC,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,iBAAiB,EAAE;gBAClE,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjB,sBAAsB;YACtB,KAAK,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC;QAED,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,wEAAwE;QACxE,MAAM,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7C,IACE,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC,MAAM;YACpC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBAC/B,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC;YAC/B,CAAC,CAAC,EACF,CAAC;YACD,MAAM,EAAC,QAAQ,EAAC,GAAG,MAAM,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACjE,sEAAsE;YACtE,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,uBAAuB,EAAE;gBACzD,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,2CAA2C;YAC3C,MAAM,KAAK,CAAC,YAAY,CACtB,MAAM,CAAC,SAAS,aAAa;gBAC3B,IAAI,CAAC,aAAa,CAChB,IAAI,KAAK,CAAC,QAAQ,EAAE;oBAClB,OAAO,EAAE,IAAI;iBACd,CAAC,CACH,CAAC;YACJ,CAAC,CAAC,EACF,KAAK,EACL,MAAM,CAAC,OAAO,CACf,CAAC;QACJ,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,iBAAiB,CACf,MAAsC,EACtC,UAAsB;QAEtB,MAAM,aAAa,GAAqB,EAAE,CAAC;QAC3C,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACpC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,uCAAuB,CAAC,CAAC,CAAC;oBACxB,MAAM,CAAC,UAAU,KAAK,EAAC,WAAW,uCAAyB,EAAC,CAAC;oBAC7D,MAAM,CAAC,UAAU,CAAC,WAAW,0CAA4B,CAAC;oBAE1D,MAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CACnC,MAAM,CAAC,EAAE,sCAET,MAAM,CAAC,UAAU,CAAC,WAAW,CAC9B,CAAC;oBACF,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;wBACrD,MAAM,IAAI,wBAAwB,CAChC,yBAAyB,MAAM,CAAC,EAAE,UAAU,MAAM,CAAC,OAAO,SAAS,MAAM,CAAC,UAAU,CAAC,WAAW,GAAG,CACpG,CAAC;oBACJ,CAAC;oBACD,gEAAgE;oBAChE,MAAM,CAAC,eAAe,EAAE,CAAC;oBACzB,MAAM;gBACR,CAAC;gBACD;oBACE,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAkB,CAAC,CAAC;YACjE,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAC5C,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,MAAM,EAAE,IAAI;aACb,CAAC,CAAC,CAAC;YACJ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC/B,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzB,CAAC;gBACD,aAAa,CAAC,CAAC,CAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAE,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;CACF"}